## [1.5.2] - 2025-05-27

- feat: add automated version management and release system (857f384)
- Merge pull request #260 from blaze-commerce/WOOLESS-7494_Fix-WooCommerce-color-hex-values-not-syncing-for-product-variations-in-Typesense_Lan (5a5ca11)
- Merge branch 'main' into WOOLESS-7494_Fix-WooCommerce-color-hex-values-not-syncing-for-product-variations-in-Typesense_Lan (ce11abf)
- Merge pull request #259 from blaze-commerce/WOOLESS-7495_Fix-Typesense-price-sync-by-converting-tax-prices-to-integers_Lan (3671240)
- feat: remove fallback color mappings from WoocommerceVariationSwatches (902acf5)
- fix(tax): convert price fields to int64 for Typesense compatibility (2dd134b)
- Merge pull request #253 from blaze-commerce/wp-navigation (dcc2e5c)
- Merge branch 'main' into wp-navigation (6fda0a6)
- Merge pull request #251 from blaze-commerce/product-bundle-changes (cac93d4)
- fix: merge conflict (67d9079)
- Merge branch 'main' of https://github.com/blaze-commerce/blazecommerce-wp-plugin (14c3fcc)
- fix: bundle sync error because of tax (71ba036)
- feat: save cookie domain to typesense (caa0bf0)
- fix: error class not found (ba897c3)
- Merge pull request #255 from blaze-commerce/kajal-naina-multicurrency-changes (7b8577a)
- Merge branch 'main' into kajal-naina-multicurrency-changes (e456f26)
- Merge pull request #256 from blaze-commerce/WOOLESS-6845_Header-Menu-and-mega-menu_Lan (ad9b4c8)
- fix: merge error (c91a280)
- fix: graphql cart item total and sub total for bundle products (c1b8970)
- build: additional settings for allowed permalinks (e066040)
- build: update build files for menuLinkActiveBackgroundColor implementation (caa4998)
- feat(maxmegamenu): add menuLinkActiveBackgroundColor attribute for active menu styling [86et444cf] (16781e7)
- feat: Enhance price metadata filters for tax-inclusive and exclusive pricing (df9586a)
- fix: incorrect cart item total and subtotal for bundle cart item (c3ef17e)
- feat: save navigation to typesense (d921b9d)
- Merge branch 'main' of https://github.com/blaze-commerce/blazecommerce-wp-plugin (9ee5fe2)
- Merge pull request #252 from blaze-commerce/WOOLESS-7338_Add-nonvariants-parameter-to-BlazeCommerce-product-sync-CLI-command_Lan (d02cbc0)
- feat: Add --nonvariants parameter to product sync CLI command (595cfbf)
- feat: get custom meta data (0e73caf)
- Merge branch 'main' of https://github.com/blaze-commerce/blazecommerce-wp-plugin (fad0d99)
- Merge pull request #248 from blaze-commerce/collection-settings (cb98448)
- Merge branch 'collection-settings' of https://github.com/blaze-commerce/blazecommerce-wp-plugin into collection-settings (e813b6b)
- feat: add necessary bundle data to typesense (318aea4)
- Merge branch 'main' into collection-settings (5131e64)
- feat: modify stock status for bundle (a385ceb)
- feat: make permalink facetable (1fa95fc)
- feat: url query base session passing (abff908)
- Merge pull request #249 from blaze-commerce/tax-by-location (9c0743c)
- Merge branch 'main' of https://github.com/blaze-commerce/blazecommerce-wp-plugin (ce5eff3)
- feat: enhance price metadata to include location-based pricing with and without tax (e35f23c)
- feat: add menu id to typesense (a5d15ca)
- feat: adding the custom icon to typesense (f4b1678)
- Merge pull request #247 from blaze-commerce/tax-settings-and-table-rates (d71f1ba)
- feat: implement tax settings retrieval and update tax rates handling (898496e)
- fix: sync error when syncing through the ui and post data is empty (845bbbb)
- fix: sync error when we exclude pages and object batch is empty (593b8b6)
- feat: add filter to exclude a page from syncing to typesense (9b3b82d)
- fix: sync error because of incorrect type (3e99922)
- refactor: make menu id string (45e1f96)
- feat: save and reformat real wp menu items (2c5e49a)
- feat: add classes (7c09853)
- feat: settings to allow devs to not sync product, taxonomy and page collections (022969c)
- Merge branch 'main' of https://github.com/blaze-commerce/blazecommerce-wp-plugin (1cdbaa2)
- feat: add tax settings to additional site info and implement tax rates retrieval (504327c)
- Merge pull request #245 from blaze-commerce/product-settings (2cb86b3)
- Merge branch 'main' into product-settings (51a5cd7)
- Merge branch 'main' of https://github.com/blaze-commerce/blazecommerce-wp-plugin (1abb041)
- Merge pull request #246 from blaze-commerce/product-child-variant-cli-sync (9a74f69)
- Merge branch 'main' into product-child-variant-cli-sync (a46ff41)
- feat: add support for syncing product variants in CLI command (b127bd7)
- feat: add product settings in site info (3e198e6)
- Merge pull request #244 from blaze-commerce/WOOLESS-6518_Fix-NaN-Product-Price_Zildjian-Jax-Olis (a3f983c)
- feat: add immediate update for Typesense variations during product import (337e145)
- Merge pull request #243 from blaze-commerce/feat/category-product-per-page (8264985)
- feat: add a settings from woocommerce for product per page (2d8fc28)
- Merge pull request #229 from blaze-commerce/feat/load-frontend-styles-to-backend (2c31301)
- Merge branch 'main' into feat/load-frontend-styles-to-backend (d3ac614)
- Merge pull request #241 from blaze-commerce/feat/settings-for-page-headless (af8521b)
- Merge branch 'main' into feat/settings-for-page-headless (3fc84e1)
- Merge pull request #240 from blaze-commerce/feat/pushing-custom-post-type-to-typesense (0949cc0)
- feat: add filter for settings so that developer can filter on it (17e103b)
- Merge branch 'main' into feat/pushing-custom-post-type-to-typesense (b1e389c)
- Merge pull request #239 from blaze-commerce/WOOLESS-6252_Add-Blog-Content-based-on-Figma-Template_Lan (ca8bf62)
- refactor: use blazecommerce/collection/page/typesense_data initially (2596c3f)
- Merge pull request #238 from blaze-commerce/WOOLESS-6252_Add-Blog-Content-based-on-Figma-Template_Lan (1b52958)
- refactor: Added spaces around operators and after commas to comply with WordPress coding standards. (99459e9)
- Merge branch 'main' into WOOLESS-6252_Add-Blog-Content-based-on-Figma-Template_Lan (a3941de)
- fix: used as3cf_filter_post_local_to_provider instead (23fbcb8)
- refactor: clean code and wordpress standards (17cac3c)
- feat: replace domains for url key (e60468e)
- fix: added condition to check if filter exists (987e3fd)
- refactor: check if AS3CF_SETTINGS is defined (4df236f)
- refactor: removed esc_url() (e955936)
- refactor: change required plugin (079c943)
- Revert "feat: replace url key image links" (cc6a94d)
- feat: replace url key image links (af9e8ce)
- Revert "feat:  added url key for replacing image domain and fix for missing version paths and malformed URLs" (9428ba3)
- feat:  added url key for replacing image domain and fix for missing version paths and malformed URLs (adbd5fb)
- Merge pull request #237 from blaze-commerce/WOOLESS-6368_All-of-the-items-in-the-BlanketsTubesWraps-section-have-a-Best-Seller-tag-on-them-How-do-we-update-this_Ridwan-Arifandi (e659fee)
- feat: add an extra option to override the best seller function (1704f3e)
- Merge pull request #236 from blaze-commerce/WOOLESS-6249_Fix-Images-are-not-displaying_Lan (e2d7e1d)
- Merge branch 'main' into WOOLESS-6249_Fix-Images-are-not-displaying_Lan (bf91d3d)
- Merge branch 'main' into feat/load-frontend-styles-to-backend (293cd2b)
- Merge pull request #234 from blaze-commerce/refactor/remove-cart-page-from-sync-exclusion (7b3ad0e)
- refactor: improved code according to wordpress standards (19aef6d)
- feat: added WP Offload Media extension (362edbb)
- refactor: taxonomies data and add filter for each term (e6b171b)
- fix: sync issue because of acf error (febf7b2)
- feat: add acf fields to typesense (4fafe64)
- refactor: udpate the page collection filter (e70a9eb)
- feat: add a filter for page collection fields (b7c086f)
- fix: incorrect data for getting post_type (c7de301)
- feat: add a filter for the custom post type which will allow others to dynamically sync custom post types to page collections (91cbd27)
- Merge branch 'main' into refactor/remove-cart-page-from-sync-exclusion (6f5b615)
- Merge pull request #235 from blaze-commerce/WOOLESS-6190_Fix-missing-WooCommerce-Subscriptions-extension_Lan (a663b05)
- Merge branch 'main' into WOOLESS-6190_Fix-missing-WooCommerce-Subscriptions-extension_Lan (6160a95)
- Merge pull request #233 from blaze-commerce/WOOLESS-4728_Product-Page-Shipping-Returns-Warranty-FAQ_Ridwan-Arifandi (6e8a0e7)
- Merge branch 'main' into refactor/remove-cart-page-from-sync-exclusion (bf61dcb)
- fix: renamed registered extension (2ee103f)
- Merge branch 'main' into WOOLESS-4728_Product-Page-Shipping-Returns-Warranty-FAQ_Ridwan-Arifandi (1e4082e)
- Merge pull request #230 from blaze-commerce/feat/add-no-redirect-flag (40b44c9)
- Merge branch 'main' into feat/add-no-redirect-flag (3d87a9f)
- feat: set zipcode required (ad258d8)
- Merge pull request #232 from blaze-commerce/WOOLESS-4752_Product-Page-Product-Subscription_Ridwan-Arifandi (f2536d6)
- Merge branch 'main' into WOOLESS-4752_Product-Page-Product-Subscription_Ridwan-Arifandi (ec83140)
- feat: add extra options for subscription (2f3b663)
- Merge pull request #231 from blaze-commerce/WOOLESS-6096_Flycart-discountpromo-plugin-support_Zildjian-Jax-Olis (e30427a)
- feat: add action to handle cart item updates for auto-added products (c607c94)
- feat: add cart page slug to site info (4f0968f)
- feat: remove cart page from page exclusion (06c766b)
- Merge pull request #228 from blaze-commerce/develop (f5f0ef5)
- feat: add a check if page is requested from local and don't redirect it (4e524b7)
- feat: add no redirect and refactor cli sync (a048623)
- fix: acf datatype set to string (69eef76)
- Merge branch 'develop' of https://github.com/blaze-commerce/blazecommerce-wp-plugin into develop (4e97fdc)
- fix: change type from auto[] to auto (646617e)
- feat: add checkbox to enable geo restriction (31fda65)
- Merge branch 'main' into develop (49765dc)
- Merge pull request #222 from blaze-commerce/feat/set-new-field-term-id (9ed4a90)
- Merge branch 'main' into feat/set-new-field-term-id (47cc7c7)
- fix: merge change (500b079)
- Merge pull request #226 from blaze-commerce/fix/bundle-not-saving-to-typsense (572f7f3)
- Merge branch 'main' into fix/bundle-not-saving-to-typsense (44d1ded)
- Merge pull request #227 from blaze-commerce/fix/fatal-error-missing-cli-class (63a4574)
- style: load style from frontend (6d85029)
- feat: add a field to set how many product sold can be set as best seller (3f968ea)
- fix: deprecated function (dc6bdfd)
- Merge branch 'main' into develop (d7cb852)
- fix: use curly bracket for conditional (e32f9f9)
- fix: remove logging (f0fe628)
- Merge branch 'main' into fix/bundle-not-saving-to-typsense (19884cf)
- feat: complete ACF library (f535a76)
- feat: complete ACF library integration (699297d)
- feat: complete library for WooCommerce Subscription library (5f7a573)
- remove WooCommerceSubscription.php (mistypo) (a2b6c01)
- feat: complete library for WooCommerce All Products for Subscriptions (ae3f246)
- Merge branch 'main' into fix/fatal-error-missing-cli-class (66ed3b8)
- Merge pull request #224 from blaze-commerce/feat/cli-sync (5186250)
- Merge branch 'main' into feat/cli-sync (35cf840)
- fix: fatal error class WP_CLI_Command not found (c5c2ec0)
- Merge branch 'main' into WOOLESS-5970_Product-Page-Syncronize-Check_Ridwan-Arifandi (50cbe9d)
- feat: subscription integration (39716f2)
- fix: Field undle.minPrice.USD must be float (8f94898)
- Merge pull request #225 from blaze-commerce/develop (d991de6)
- remove debug lines (f284b00)
- Merge branch 'main' into develop (71bcfaa)
- Merge branch 'main' of https://github.com/blaze-commerce/blazecommerce-wp-plugin (23cce7a)
- fix: issue with addons sync and unsupported fields (4fcb058)
- fix: php warning trying to access array offset on value of bool (152c250)
- fix: syntax error, missing comma (61f7508)
- fix: php warning trying to access array offset on value of bool (ac66267)
- fix: php warning trying to access array offset on value of bool (5e8dee4)
- feat: add time tracking to know how much time it complete the sync (9023488)
- refactor: update comments and uncomment product sync init on page 1 (07ebf01)
- fix: undefined array key error (0734dee)
- fix: warning error when syncing taxonomies (1b2f748)
- fix: critical error syncing taxonomy (a830b9c)
- feat: taxonomy cli sync (b2ade0b)
- feat: cli sync for site info (f2402a0)
- feat: wp cli sync menu (1662cad)
- feat: cli page and post sync (8f12bd0)
- feat: cli sync for products (224b689)
- Merge pull request #223 from blaze-commerce/max-mega-menu-sync-fix (98885c2)
- Merge branch 'main' into max-mega-menu-sync-fix (3f25844)
- Merge pull request #217 from blaze-commerce/WOOLESS-5215_Category-Page-Pinterest-Plugin_Zildjian-Jax-Olis (1366294)
- Merge branch 'main' into WOOLESS-5215_Category-Page-Pinterest-Plugin_Zildjian-Jax-Olis (3fb8bb9)
- Merge branch 'main' into max-mega-menu-sync-fix (34b8eb3)
- Merge pull request #218 from blaze-commerce/WOOLESS-4867_Category-Page-Product-Thumbnail-and-details_Zildjian-Jax-Olis (16cb69b)
- fix formatting issue (fc54383)
- Merge branch 'main' into WOOLESS-5215_Category-Page-Pinterest-Plugin_Zildjian-Jax-Olis (dab240f)
- fix menu sync due to max mega menu error (0fd0337)
- Merge branch 'main' into feat/woo-discount-rules (67a0f2a)
- Merge branch 'main' into WOOLESS-4867_Category-Page-Product-Thumbnail-and-details_Zildjian-Jax-Olis (2874200)
- feat: save facetable product term Id (2618e9b)
- feat: set a new field term id because ts doesnt' allow filter or query by id (4b32807)
- Merge pull request #220 from blaze-commerce/product-update (2a49954)
- Merge branch 'main' into product-update (201534e)
- Merge pull request #221 from blaze-commerce/fix-wp-cor-issue (ae19515)
- fix: wp page cors error (d33c3dd)
- refactor: updating product on order status change and product meta update (2125143)
- Merge pull request #219 from blaze-commerce/saving-woocommerce-permalink (708cea3)
- make sure to push the wp_template_part to site_info for the product cards template (dc5d576)
- fix: sort the addons based on the priority (e20f87e)
- add config for showing pinterest share button (001f0c0)
- fix: pages not revalidating because of incorrect url endpoint (67fba33)
- feat: saving woocommerce permalink structure (e1b84ff)
- remove inspect hooks (17b40c2)
- Merge commit '6aa9bd181811965eaa3cbbf40a93de65152a2997' into feat/woo-discount-rules (b8b97f6)
- feat: implement plugin woo-discount-rules (0a5ee81)
- Merge pull request #215 from blaze-commerce/page-frontend-revalidation (6aa9bd1)
- feat: revalidate next.js page urls (09bcc6e)
- fix: issue with aelia inactive (220dc56)
- Merge pull request #214 from blaze-commerce/feat/gift-card (f3962df)
- Merge commit '6f7bce161b370cde4d78b257ed55f51d3528a5d1' into feat/gift-card (22ad87b)
- fix: issue if aelia is not active (2b61512)
- Merge pull request #213 from blaze-commerce/WOOLESS-5364_400-when-accessing-backend_Zildjian-Jax-Olis (6f7bce1)
- make sure to clear existing yith_wcwl cookie before adding a new one (18ecfb5)
- Merge pull request #212 from blaze-commerce/feat/gift-card (49ab188)
- Merge branch 'main' into feat/gift-card (972b66e)
- Merge pull request #211 from blaze-commerce/bbc-sync-error (dbb4d40)
- fix: syncing nan error and prevent simultaneous request of ajax to sync items as it causes the server to not respond (483a043)
- Merge pull request #210 from blaze-commerce/WOOLESS-4962_Menu-Mobile-still-not-fix_Lan (08ff27c)
- fix: bring back missing desktop submenu items (80b64fe)
- Merge pull request #209 from blaze-commerce/WOOLESS-4962_Menu-Mobile-still-not-fix_Lan (17be9ac)
- refactor: added 3rd submenu items with children for menus (31917aa)
- Merge commit 'aba6ff49952a74500908ff410a1cb7207a08b2cc' into feat/gift-card (7a452c1)
- Merge branch 'feat/gift-card' of https://github.com/blaze-commerce/blazecommerce-wp-plugin into feat/gift-card (971fa01)
- feat: add gift card element (c34b3ef)
- Merge pull request #208 from blaze-commerce/fix-not-all-published-products-are-synced (aba6ff4)
- get all products and filter published manually (528a325)
- Merge pull request #206 from blaze-commerce/feat/gift-card (d3be0d5)
- Merge branch 'main' into feat/gift-card (b2eadd3)
- Merge pull request #207 from blaze-commerce/fix-sync-request-spamming (6a70687)
- prevent sync from spamming api requests (7ac7d99)
- fix: gift metadata, for the price value type, it must be float (a6264d0)
- fix: gift metadata (22df836)
- fix: gift card min price if allowed custom amount (6e1c25b)
- Merge pull request #205 from blaze-commerce/WOOLESS-5017_Shop-Wholesale-issue_Zildjian-Jax-Olis (fd61450)
- wholesale feature changes (8b96c6a)
- Merge pull request #204 from blaze-commerce/add-ability-to-redeploy (bb7377f)
- Merge branch 'main' into add-ability-to-redeploy (66dbfdd)
- Merge pull request #203 from blaze-commerce/setting-product-categories (1a54d31)
- refactor: remove unnecessary codes (69122b2)
- refactor: include parent categories to taxonomies in typesense (473b11f)
- add back the ability to redeploy through the plugin (ac611e2)
- Merge pull request #198 from blaze-commerce/adding-shop-page-slug (ad31cf6)
- Merge branch 'main' into adding-shop-page-slug (71f7a11)
- fix: available variation is not empty even though variantions are out of stock (a727cfb)
- Merge pull request #202 from blaze-commerce/WOOLESS-5062_Fix-Categories-missing_Lan (8b4101d)
- fix: set last parameter to true to not delete any difference in terms (91d0116)
- fix: variable product out of stock because stock level is 0 for the variable product (8797419)
- fix: sync for product not working because of incorrect currencies (7825181)
- Merge pull request #201 from blaze-commerce/revalidate-taxonomies-on-product-update (4b4d082)
- feat: enhance revalidation process to include taxonomy URLs for products (a49be5c)
- fix: table heading is not correct when we sync all products (da0c24a)
- feat: addong woocommerce product tabs and support for ni variation table plugin (90ee837)
- Merge pull request #184 from blaze-commerce/WOOLESS-3054_Footer-Join-our-vip-list_Lan (015578a)
- Merge branch 'main' into WOOLESS-3054_Footer-Join-our-vip-list_Lan (37e219e)
- fix: variation thumbnail not saving correctly (dc89877)
- refactor: wp db query code formatting (6ededb5)
- feat: add blog page for post breadcrumbs (4007655)
- feat: add blog page for post breadcrumbs (e274010)
- Merge branch 'main' into adding-shop-page-slug (98997a5)
- Merge pull request #199 from blaze-commerce/page-collection-not-having-full-data (c61ec16)
- feat: add breadcrumbs to post & pages (4476e6b)
- fix: where certain post item is shown in the next page and causes the data in typesense to be incomplete (b08a761)
- feat: adding blog page slug to typesense (306f25e)
- fix: yoast not adding title tag (323af3a)
- Merge pull request #191 from blaze-commerce/smart-coupon-support (f95720b)
- format to use wp coding standards (17548c0)
- Merge branch 'main' into smart-coupon-support (e09f2f2)
- refactor: remove wishlist page from exclusion of the pages (90a92bd)
- feat: add a filter to let other develop to exclude pages on the frontend (805fbbb)
- feat: add shop page slug in typesense (c6a8228)
- Merge pull request #197 from blaze-commerce/WOOLESS-4326_Single-Product-Page-Custom-Product_Lan (85c7ef5)
- fix: added multiple_choice for addon fields (03a103e)
- Merge pull request #196 from blaze-commerce/post-pages-collection-issue (f46100e)
- fix: issue where post and pages did not sync fully sync to typesense (b53a635)
- Merge pull request #195 from blaze-commerce/specify-price-currency-types (37eb54f)
- Merge branch 'main' into specify-price-currency-types (9dcdd03)
- Merge pull request #194 from blaze-commerce/WOOLESS-4296_Single-Product-Page-Gift-Notes_Zildjian-Jax-Olis (96f6ab3)
- Specify correct field types by currency in Product and WoocommerceBundle (5e7ad52)
- Refactor WoocommerceProductAddons to handle cart item data (66302fe)
- Merge pull request #193 from blaze-commerce/WOOLESS-4509_Category-Page-Bundles-Undefined-price_Zildjian-Jax-Olis (d3b3fe7)
- Refactor bundle price conversion in WoocommerceBundle (9f36c27)
- Merge branch 'main' into smart-coupon-support (fae6547)
- Merge pull request #189 from blaze-commerce/currency-schema-not-configured-properly (fad13e5)
- Merge branch 'main' into currency-schema-not-configured-properly (5cf8654)
- Merge pull request #192 from blaze-commerce/WOOLESS-4493_Fix-Product-not-syncing-to-Typesense_Lan (32e6e1d)
- fix: sku to string type and bundle max price to float (d8e3356)
- fix indentation issues (6d906d7)
- Merge branch 'main' into smart-coupon-support (96a796b)
- Merge pull request #190 from blaze-commerce/fix-page-not-found (03bfe55)
- adding smart coupon support (fd7cda4)
- feat: delete ts product when user permanently deletes a product in wp admin (303cf6a)
- fix: product page 404 issue because slug is empty (4fe7b4b)
- fix: product page 404 issue because slug is empty (5c7c109)
- fix: product syncing error because updated at is null and is not int (b2f1849)
- fix: currency schema not configured properly (a34f218)
- Merge pull request #188 from blaze-commerce/page-template (f272638)
- Merge branch 'main' into page-template (88bb53f)
- feat: add an extra argument for performance issue (26e1830)
- Merge pull request #187 from blaze-commerce/develop (b3cb590)
- feat: exclude wishlist page from syncing to typesense (186b4d1)
- feat: exclude wishlist from pushing to typesense (288db59)
- feat: prevent syncing woocommerce pages (fe6e178)
- feat: setting page template (359da6d)
- Merge branch 'main' into develop (f0043e4)
- fix: problem when create an order manually from admin (8457dae)
- Merge pull request #186 from blaze-commerce/pushing-theme-to-typesense (95accd1)
- refactor: change name for them (2b43c6d)
- feat: pushing current theme configs to typsense (a52b708)
- Merge branch 'main' into WOOLESS-3054_Footer-Join-our-vip-list_Lan (b77a8c8)
- Merge pull request #185 from blaze-commerce/WOOLESS-4313_The-product-feeds-seems-to-have-disappeared_Zildjian-Jax-Olis (610c75e)
- Make sure tax related modification will not trigger on wc api endpoints (fee4fb1)
- Merge branch 'main' into WOOLESS-3054_Footer-Join-our-vip-list_Lan (c4f37bb)
- test: added klaviyo script for staging purposes only (099f4a7)
- Merge pull request #183 from blaze-commerce/adding-author-info (0d0162b)
- refactor: change field keys to use camelcase (37863d8)
- feat: adding basic author info to the post/page collections (5880c88)
- Merge pull request #182 from blaze-commerce/WOOLESS-3059_Category-Page-Product-Filtering_Zildjian-Jax-Olis (014acec)
- create grouped sub category filter (fe925a3)
- Merge pull request #181 from blaze-commerce/sync-issue-because-price (c9176ee)
- Merge branch 'main' into sync-issue-because-price (6fa688c)
- Merge pull request #179 from blaze-commerce/fix-sync-load-time-woo-addons (383b625)
- Merge branch 'main' into fix-sync-load-time-woo-addons (d759f65)
- fix: not all product is pushed to typesense becuase of data type issue (b767222)
- Merge pull request #178 from blaze-commerce/WOOLESS-3150_Footer-FAQs_Lan (9fe205d)
- feat: added formatting to current date shortcode (7f9a8d9)
- fetch general addons and add to transient only load addons and reviews on initialization of product (9c2d996)
- feat: added current year shortcode (608932f)
- Merge pull request #177 from blaze-commerce/WOOLESS-3398_Padding-left--right_Lan (fc3abea)
- feat: added container width and padding config (9df53fd)
- Merge pull request #176 from blaze-commerce/WOOLESS-4048_Product-Page---Gutenberg_Ridwan-Arifandi (3cfcba7)
- Merge commit '00e4d09c85f74bd3d3095fab03eb57a73b1160d2' into WOOLESS-4048_Product-Page---Gutenberg_Ridwan-Arifandi (66e0502)
- Merge pull request #175 from blaze-commerce/insert-header-footer-on-plugin-activate (00e4d09)
- fix: default settings for header and footer not saved on plugin activate (9b1aae6)
- Merge pull request #174 from blaze-commerce/remove-redeploy (f00a265)
- refactor: clean up and remove redploy frontend related codes (a876f00)
- Merge pull request #173 from blaze-commerce/fix-fatal-error (d9c495f)
- fix: fatal error because of unknow char (fce3226)
- Merge pull request #172 from blaze-commerce/site-info-fatal-error-after-sync (be9519e)
- Merge branch 'develop' into site-info-fatal-error-after-sync (7a49922)
- Merge pull request #165 from blaze-commerce/typsense-sync-optimization (dc7eddf)
- Merge branch 'develop' into typsense-sync-optimization (0dde939)
- fix: fatal error because array key don't exist (923ecdd)
- Merge pull request #171 from blaze-commerce/header-footer-settings (d794b7d)
- fix: header and footer not published when creating it and fix fatal error when visiting footer and header (b6434d1)
- Merge pull request #170 from blaze-commerce/WOOLESS-4159_update-the-bc-plugin-with-toggle-activeinactive_Ridwan-Arifandi (2a69bad)
- fix: implement bracelets for wp standard coding (9facdaa)
- Merge commit '230ebee461dd1f290ada833b42cd2646cd33f816' into WOOLESS-4159_update-the-bc-plugin-with-toggle-activeinactive_Ridwan-Arifandi (f6b2006)
- feat: add feature to activate/deactivate woocommerce related function (8a86ca1)
- Merge pull request #169 from blaze-commerce/fix-fatal-activation-error (230ebee)
- Merge branch 'develop' into fix-fatal-activation-error (76ffc5d)
- refactor: remove unnecessary new lines (37f7731)
- Merge pull request #168 from blaze-commerce/WOOLESS-4137_Cant-access-wp-admin-Products--All-Products_Zildjian-Jax-Olis (cb2960a)
- fix: fatal activation error because the code is trying to push data to typesense (ef37dd6)
- fix Product page 500 issue set shipping country based on matched country from aelia (7beb506)
- feat: implement gutenberg template builder (8a7224f)
- Merge pull request #162 from blaze-commerce/WOOLESS-3953_Product-Page-Gift-Voucher_Ridwan-Arifandi (d1d0058)
- Merge branch 'develop' into WOOLESS-3953_Product-Page-Gift-Voucher_Ridwan-Arifandi (4bb5d25)
- Merge pull request #167 from blaze-commerce/bundle-product-settings (edfc7e4)
- Merge branch 'develop' into bundle-product-settings (06ee0ec)
- Merge pull request #163 from blaze-commerce/WOOLESS-4037_use-the-placeholder-image-from-Woo-settings_Jameshwart-Lopez (e5989ca)
- Merge branch 'develop' into WOOLESS-4037_use-the-placeholder-image-from-Woo-settings_Jameshwart-Lopez (2b3f3bd)
- Merge pull request #161 from blaze-commerce/homepage-tab-to-edit-homepage (bfed195)
- Merge branch 'develop' into homepage-tab-to-edit-homepage (2a2047e)
- feat: add a settings for bundle product to enable frontend to check if BundleProduct is enabled (0e533ae)
- Merge pull request #166 from blaze-commerce/default-header-search (7df6e81)
- feat: adding default search container block (1d0492a)
- create a queue when importing products and taxonomies set the batch to 5 lot of code changes to the sync js file (f657a3e)
- Merge pull request #164 from blaze-commerce/WOOLESS-4048_Product-Page---Gutenberg_Ridwan-Arifandi (4b6f82c)
- fix: update tgmpa configuration and add requires plugins setting. since WooCommerce needs to be activated first before BlazeCommerce plugin (f28a2b8)
- update tgmpa script (983ccdb)
- fix: remove plugins (978c170)
- feat: add external plugins since TGMPA cant extract the zip files from github (44a75a9)
- feat: use wc settings of placeholder image if a product doesn't have image (a4049ff)
- feat: add TGMPA library (9a07e91)
- fix: add conditional check if any extension plugin not active (d1e972c)
- fix: wrong variable (35486bc)
- Merge branch 'develop' into WOOLESS-3953_Product-Page-Gift-Voucher_Ridwan-Arifandi (629bbf1)
- Revert "fix: wrong variable for product price" (7da4520)
- fix: wrong variable for product price (1bba18d)
- feat: redirect homepage tab to gutenberg edit page (5c00856)
- Merge branch 'WOOLESS-3953_Product-Page-Gift-Voucher_Ridwan-Arifandi' of https://github.com/blaze-commerce/blaze-commerce into WOOLESS-3953_Product-Page-Gift-Voucher_Ridwan-Arifandi (8d735ba)
- fix: wrong put variable (c9d93cd)
- Merge pull request #160 from blaze-commerce/WOOLESS-3953_Product-Page-Gift-Voucher_Ridwan-Arifandi (e909644)
- Merge branch 'develop' into WOOLESS-3953_Product-Page-Gift-Voucher_Ridwan-Arifandi (3379499)
- Merge pull request #159 from blaze-commerce/WOOLESS-3415_Default-Setup-for-header--footer_Jameshwart-Lopez (4e649ac)
- Merge branch 'develop' into WOOLESS-3953_Product-Page-Gift-Voucher_Ridwan-Arifandi (b22b35b)
- fix: double checking if the gift card product has no price, idk why the problem is happening (e91902b)
- fix: remove unneeded code (2449cdc)
- fix: gift card price sync (4ef69c6)
- feat: update bundle configuration (327d45d)
- Merge branch 'develop' into WOOLESS-3415_Default-Setup-for-header--footer_Jameshwart-Lopez (77a2658)
- Merge pull request #158 from blaze-commerce/jv-lopez-patch-2 (da554b3)
- feat: set default home page on plugin activate (e0d2d04)
- feat: insert default header and footer if not exist (c5a59e3)
- feat: add gift card product type (9658d2f)
- Merge branch 'develop' into jv-lopez-patch-2 (0b0612c)
- Merge pull request #156 from blaze-commerce/fix-test-connection (abf52d6)
- Merge branch 'develop' into jv-lopez-patch-2 (11f007e)
- Merge branch 'develop' into fix-test-connection (70bb2d6)
- Merge pull request #157 from blaze-commerce/jv-lopez-patch-1 (e71ee60)
- Create license.txt (db35940)
- Create TRADEMARK.md (9be2c97)
- Merge pull request #154 from blaze-commerce/maybe-use-parent-thumbnail (28fe414)
- fix: merge conflict (7e3465e)
- Merge pull request #155 from blaze-commerce/depend-variation-status-to-parent-status (8b6417a)
- fix: incorrect site info collection name (6a91866)
- fix: taxonomy not having the correct store id (05718e0)
- refactor: remove unnecessary code (dd90d1e)
- fix: connection because the collections is not yet created (9b15f32)
- fix: issue where variation is still showing because status is published even though parent is not published (a5cef46)
- fix: thumbnail not using parent when variant is empty (a1b81dc)
- feat: add the select option and a filter for font family in general settings (1823932)
- feat: use wp site icon url instead of using admin portal favicon (34bd4d7)


# Changelog

= 1.5.0 (2024-07-12): =

- Add option to enable redirection to the frontend
- Fix gutenberg issue when siteurl and homeurl are different

= 1.4.7 (2024-04-14): =

- Remove `WooDiscountRules` extension support since it's not needed.
