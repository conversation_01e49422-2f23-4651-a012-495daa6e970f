!function(){"use strict";var e=window.ReactJSXRuntime;const{PanelBody:n,SelectControl:t,__experimentalUnitControl:o}=wp.components,{__:__}=wp.i18n,l=({attributes:l,setAttributes:i})=>{const{menuId:r,menuMaxWidth:a}=l,s=(blaze_commerce_block_config.menus||[]).map((e=>({label:e.name,value:e.term_id})));return(0,e.jsxs)(n,{title:__("Blaze Commerce - General"),initialOpen:!0,children:[(0,e.jsx)("p",{}),(0,e.jsx)(t,{label:"Menu",value:r,options:[{label:"Select a menu",value:""},...s],onChange:e=>i({menuId:e}),__nextHasNoMarginBottom:!0}),(0,e.jsx)("p",{}),(0,e.jsx)(o,{label:"Max Width (px)",disableUnits:!0,onChange:e=>i({menuMaxWidth:e}),value:a})]})};l.attributeSchema={menuId:{type:"string"},menuMaxWidth:{type:"string",default:"1200x"}};const{useState:i}=wp.element,{PanelBody:r,FontSizePicker:a,SelectControl:s}=wp.components,{__:u}=wp.i18n,c=[{name:u("Extra Small"),slug:"xs",size:12},{name:u("Small"),slug:"sm",size:14},{name:u("Base"),slug:"base",size:16},{name:u("Large"),slug:"lg",size:18},{name:u("Extra Large"),slug:"xl",size:20},{name:u("2x Extra Large"),slug:"2xl",size:24}],d=({attributes:n,setAttributes:t})=>{const{fontSize:o,fontWeight:l,letterCase:i}=n;return(0,e.jsxs)(r,{title:u("Blaze Commerce - Typography"),initialOpen:!1,children:[(0,e.jsx)("p",{}),(0,e.jsx)(a,{fontSizes:c,value:parseInt(o),fallbackFontSize:16,withReset:!0,withSlider:!1,onChange:e=>t({fontSize:e})}),(0,e.jsx)("p",{}),(0,e.jsx)(s,{label:"Font weight",value:l,options:[{label:"Thin",value:"100"},{label:"Extra Light",value:"200"},{label:"Light",value:"300"},{label:"Normal",value:"400"},{label:"Medium",value:"500"},{label:"Semi Bold",value:"600"},{label:"Bold",value:"700"},{label:"Extra Bold",value:"800"}],onChange:e=>t({fontWeight:e}),__nextHasNoMarginBottom:!0}),(0,e.jsx)("p",{}),(0,e.jsx)(s,{label:"Letter case",value:i,options:[{label:"None",value:"none"},{label:"Uppercase",value:"uppercase"},{label:"Lowercase",value:"lowercase"},{label:"Capitalize",value:"capitalize"}],onChange:e=>t({letterCase:e}),__nextHasNoMarginBottom:!0})]})};d.attributeSchema={fontSize:{type:"string",default:"16"},fontWeight:{type:"string",default:"400"},letterCase:{type:"string",default:"none"}};const{useState:m}=wp.element,{ColorPicker:x,Popover:p,Button:g}=wp.components,b=({value:n,setValue:t})=>{const[o,l]=m(!1),[i,r]=m(n),[a,s]=m();console.log("rerendering?");const u=()=>{l(!1)};return console.log("selectedColor",i),(0,e.jsxs)(e.Fragment,{children:[(0,e.jsx)("div",{ref:s,onClick:()=>{console.log("clicked wow"),l(!0)},style:{width:"20px",height:"20px",borderRadius:"9999px",backgroundColor:n,cursor:"pointer",border:"1px solid #e0e0e0"}}),o&&(0,e.jsxs)(p,{anchor:a,placement:"bottom-end",position:"top left",children:[(0,e.jsx)(x,{color:i,onChange:r,enableAlpha:!0,defaultValue:"#000"}),(0,e.jsxs)("div",{style:{display:"flex",padding:"0 16px 20px",justifyContent:"flex-end",gap:"10px"},children:[(0,e.jsx)(g,{isDestructive:!0,size:"compact",onClick:()=>{r(""),t(""),u()},children:"Reset"}),(0,e.jsx)(g,{variant:"secondary",size:"compact",onClick:u,children:"Cancel"}),(0,e.jsx)(g,{variant:"primary",size:"compact",onClick:()=>{t(i),u()},children:"Save"})]})]})]})},C={top:"0px",left:"0px",right:"0px",bottom:"0px"},{store:j}=wp.editor,{select:k}=wp.data,{PanelBody:h,__experimentalBoxControl:v,__experimentalDivider:L,ToggleControl:y,Flex:B,FlexBlock:S,FlexItem:w}=wp.components,{__:f}=wp.i18n,M=({attributes:n,setAttributes:t})=>{const{menuCentered:o,menuFullWidth:l,mainNavigationBackgroundColor:i,menuLinkColor:r,menuLinkHoverColor:a,menuLinkBackgroundColor:s,menuLinkHoverBackgroundColor:u,menuLinkActiveBackgroundColor:c,menuLinkActiveColor:d,mobileMenuLinkColor:m,menuLinkPadding:x,menuLinkMargin:p,menuSeparatorColor:g,menuTextColor:C,menuHoverTextColor:M,menuBackgroundColor:_,menuHoverBackgroundColor:H,menuTextPadding:P,menuTextMargin:T}=n,z="Mobile"===k(j).getDeviceType();return(0,e.jsxs)(h,{title:f("Blaze Commerce - Main Menu"),initialOpen:!1,children:[(0,e.jsx)("p",{}),(0,e.jsx)(y,{label:"Centered",help:o?"Menu is centered.":"Menu starts on the left.",checked:o,onChange:e=>{t({menuCentered:e})}}),(0,e.jsx)("p",{}),(0,e.jsx)(y,{label:"Full Width",help:o?"Menu is full width.":"Menu width is auto.",checked:l,onChange:e=>{t({menuFullWidth:e})}}),(0,e.jsx)(L,{}),(0,e.jsx)("p",{}),(0,e.jsxs)(B,{children:[(0,e.jsx)(S,{children:"Container Background Color"}),(0,e.jsx)(w,{children:(0,e.jsx)(b,{value:i,setValue:e=>t({mainNavigationBackgroundColor:e})})})]}),(0,e.jsx)("p",{}),(0,e.jsxs)(B,{children:[(0,e.jsx)(S,{children:"Link Color"}),(0,e.jsx)(w,{children:(0,e.jsx)(b,{value:z?m:r||C,setValue:e=>t({[z?"mobileMenuLinkColor":"menuLinkColor"]:e})})}),(0,e.jsx)(w,{children:(0,e.jsx)(b,{value:a||M,setValue:e=>t({menuLinkHoverColor:e})})})]}),(0,e.jsx)("p",{}),(0,e.jsxs)(B,{children:[(0,e.jsx)(S,{children:"Link Background Color"}),(0,e.jsx)(w,{children:(0,e.jsx)(b,{value:s||_,setValue:e=>t({menuLinkBackgroundColor:e})})}),(0,e.jsx)(w,{children:(0,e.jsx)(b,{value:u||H,setValue:e=>t({menuLinkHoverBackgroundColor:e})})})]}),(0,e.jsx)("p",{}),(0,e.jsxs)(B,{children:[(0,e.jsx)(S,{children:"Active Link Color"}),(0,e.jsx)(w,{children:(0,e.jsx)(b,{value:d,setValue:e=>t({menuLinkActiveColor:e})})})]}),(0,e.jsx)("p",{}),(0,e.jsxs)(B,{children:[(0,e.jsx)(S,{children:"Active Link Background Color"}),(0,e.jsx)(w,{children:(0,e.jsx)(b,{value:c,setValue:e=>t({menuLinkActiveBackgroundColor:e})})})]}),(0,e.jsx)("p",{}),(0,e.jsxs)(B,{children:[(0,e.jsx)(S,{children:"Link Separator Color"}),(0,e.jsx)(w,{children:(0,e.jsx)(b,{value:g,setValue:e=>t({menuSeparatorColor:e})})})]}),(0,e.jsx)(L,{}),(0,e.jsx)("p",{}),(0,e.jsx)(v,{label:f("Link Padding"),values:x||P,onChange:e=>t({menuLinkPadding:e})}),(0,e.jsx)("p",{}),(0,e.jsx)(v,{label:f("Link Margin"),values:p||T,onChange:e=>t({menuLinkMargin:e})})]})};M.attributeSchema={menuCentered:{type:"boolean"},menuFullWidth:{type:"boolean"},mainNavigationBackgroundColor:{type:"string"},menuLinkColor:{type:"string"},menuLinkHoverColor:{type:"string"},menuLinkBackgroundColor:{type:"string"},menuLinkHoverBackgroundColor:{type:"string"},menuLinkActiveBackgroundColor:{type:"string"},menuLinkActiveColor:{type:"string"},mobileMenuLinkColor:{type:"string"},menuLinkPadding:{type:"object",default:C},menuLinkMargin:{type:"object",default:C},menuSeparatorColor:{type:"string"},menuTextColor:{type:"string"},menuHoverTextColor:{type:"string"},menuBackgroundColor:{type:"string"},menuHoverBackgroundColor:{type:"string"},menuTextPadding:{type:"object",default:C},menuTextMargin:{type:"object",default:C}};const{store:_}=wp.editor,{select:H}=wp.data,{PanelBody:P,__experimentalBoxControl:T,__experimentalDivider:z,ToggleControl:F,Flex:V,FlexBlock:W,FlexItem:A}=wp.components,{__:N}=wp.i18n,O=({attributes:n,setAttributes:t})=>{const{submenuFullWidth:o,submenuContainerBackgroundColor:l,submenuLinkColor:i,submenuLinkHoverColor:r,submenuLinkBackgroundColor:a,submenuLinkHoverBackgroundColor:s,mobileSubmenuLinkColor:u,submenuContainerPadding:c,submenuLinkPadding:d,submenuLinkMargin:m,submenuTextColor:x,submenuHoverTextColor:p,submenuBackgroundColor:g,submenuHoverBackgroundColor:C,submenuTextPadding:j,submenuTextMargin:k}=n,h="Mobile"===H(_).getDeviceType();return(0,e.jsxs)(P,{title:N("Blaze Commerce - Submenu"),initialOpen:!1,children:[(0,e.jsx)("p",{}),(0,e.jsx)(F,{label:"Submenu Full Width",help:o?"Submenu is full width.":"Submenu Menu width is auto.",checked:o,onChange:e=>{t({submenuFullWidth:e})}}),(0,e.jsx)(z,{}),(0,e.jsx)("p",{}),(0,e.jsxs)(V,{children:[(0,e.jsx)(W,{children:"Container Background Color"}),(0,e.jsx)(A,{children:(0,e.jsx)(b,{value:l,setValue:e=>t({submenuContainerBackgroundColor:e})})})]}),(0,e.jsx)("p",{}),(0,e.jsxs)(V,{children:[(0,e.jsx)(W,{children:"Link Color"}),(0,e.jsx)(A,{children:(0,e.jsx)(b,{value:h?u:i||x,setValue:e=>t({[h?"mobileSubmenuLinkColor":"submenuLinkColor"]:e})})}),(0,e.jsx)(A,{children:(0,e.jsx)(b,{value:r||p,setValue:e=>t({submenuLinkHoverColor:e})})})]}),(0,e.jsx)("p",{}),(0,e.jsxs)(V,{children:[(0,e.jsx)(W,{children:"Link Background Color"}),(0,e.jsx)(A,{children:(0,e.jsx)(b,{value:a||g,setValue:e=>t({submenuLinkBackgroundColor:e})})}),(0,e.jsx)(A,{children:(0,e.jsx)(b,{value:s||C,setValue:e=>t({submenuLinkHoverBackgroundColor:e})})})]}),(0,e.jsx)(z,{}),(0,e.jsx)("p",{}),(0,e.jsx)(T,{label:N("Container Padding"),values:c,onChange:e=>t({submenuContainerPadding:e})}),(0,e.jsx)("p",{}),(0,e.jsx)(T,{label:N("Link Padding"),values:d||j,onChange:e=>t({submenuLinkPadding:e})}),(0,e.jsx)("p",{}),(0,e.jsx)(T,{label:N("Link Margin"),values:m||k,onChange:e=>t({submenuLinkMargin:e})})]})};O.attributeSchema={submenuFullWidth:{type:"boolean"},submenuContainerBackgroundColor:{type:"string"},submenuLinkColor:{type:"string"},submenuLinkHoverColor:{type:"string"},submenuLinkBackgroundColor:{type:"string"},submenuLinkHoverBackgroundColor:{type:"string"},mobileSubmenuLinkColor:{type:"string"},submenuContainerPadding:{type:"object",default:C},submenuLinkPadding:{type:"object",default:C},submenuLinkMargin:{type:"object",default:C},submenuTextColor:{type:"string"},submenuHoverTextColor:{type:"string"},submenuBackgroundColor:{type:"string"},submenuHoverBackgroundColor:{type:"string"},submenuTextPadding:{type:"object",default:C},submenuTextMargin:{type:"object",default:C}};const{createHigherOrderComponent:I}=wp.compose,{Fragment:E}=wp.element,{InspectorControls:D,store:R}=wp.editor,{addFilter:U}=wp.hooks,{select:G}=wp.data,J=["maxmegamenu/location"];U("blocks.registerBlockType","extend-block-example/attribute/spacing",((e,n)=>J.includes(n)?(e.attributes=Object.assign(e.attributes,Object.assign({},l.attributeSchema,d.attributeSchema,M.attributeSchema,O.attributeSchema)),e):e)),U("editor.BlockEdit","extend-block-example/with-spacing-control",I((n=>t=>J.includes(t.name)?(0,e.jsxs)(E,{children:[(0,e.jsx)(n,{...t}),(0,e.jsxs)(D,{children:[(0,e.jsx)(l,{...t}),(0,e.jsx)(M,{...t}),(0,e.jsx)(O,{...t}),(0,e.jsx)(d,{...t})]})]}):(0,e.jsx)(n,{...t})),"withSpacingControl")),U("blocks.getSaveContent.extraProps","extend-block-example/get-save-content/extra-props",((e,n,t)=>{if(!J.includes(n.name))return e;const o={small:"5px",medium:"15px",large:"30px"};return t.spacing in o&&(e=Object.assign(e,{style:{"margin-bottom":o[t.spacing]}})),e}))}();