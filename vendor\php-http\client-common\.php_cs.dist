<?php

/*
 * Configuration for fabpot/php-cs-fixer
 *
 * @link https://github.com/FriendsOfPHP/PHP-CS-Fixer
 */

$finder = PhpCsFixer\Finder::create()
    ->in('src')
    ->in('spec')
;
return PhpCsFixer\Config::create()
    ->setRules([
        '@PSR2' => true,
        '@Symfony' => true,
        'array_syntax' => [
            'syntax' => 'short',
        ],
        'no_empty_phpdoc' => true,
        'phpdoc_to_comment' => false,
        'single_line_throw' => false,
    ])
    ->setFinder($finder);
